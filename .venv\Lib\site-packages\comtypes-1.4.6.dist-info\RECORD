../../Scripts/clear_comtypes_cache.exe,sha256=ylbx3mRsH4E0DW1S_KoR0gAyjZX6FttsgvS_hqDQwSc,108398
comtypes-1.4.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
comtypes-1.4.6.dist-info/LICENSE.txt,sha256=Oxdn8BCYC0aSayO_Cvzl1y8zWe5eKye6ynG5tCCas4M,1273
comtypes-1.4.6.dist-info/METADATA,sha256=BHc1aXtXFW34An7xUDqE-_3G6GVZ5C-0vftDtep80tI,6446
comtypes-1.4.6.dist-info/RECORD,,
comtypes-1.4.6.dist-info/WHEEL,sha256=R0nc6qTxuoLk7ShA2_Y-UWkN8ZdfDBG2B6Eqpz2WXbs,91
comtypes-1.4.6.dist-info/entry_points.txt,sha256=Qv2GxaRYrKn2xxEqjcg2BbqNzAP8PZp4rYfBUVpvag0,67
comtypes-1.4.6.dist-info/top_level.txt,sha256=bxrAJZpBG5XMT64eKx908THCbupyI0ywd7cYIlx4Uco,9
comtypes/GUID.py,sha256=lIddw2jdNvK4GxZC_Lj9583mPyMJkL1d9RHTv7U8YEA,2878
comtypes/__init__.py,sha256=aiGkqAriU_nwmUsMaHD3ewmSIkiGDNl60RrCvFUvhvY,8641
comtypes/__pycache__/GUID.cpython-37.pyc,,
comtypes/__pycache__/__init__.cpython-37.pyc,,
comtypes/__pycache__/_comobject.cpython-37.pyc,,
comtypes/__pycache__/_idl_stuff.cpython-37.pyc,,
comtypes/__pycache__/_memberspec.cpython-37.pyc,,
comtypes/__pycache__/_meta.cpython-37.pyc,,
comtypes/__pycache__/_npsupport.cpython-37.pyc,,
comtypes/__pycache__/_py_instance_method.cpython-37.pyc,,
comtypes/__pycache__/_safearray.cpython-37.pyc,,
comtypes/__pycache__/_tlib_version_checker.cpython-37.pyc,,
comtypes/__pycache__/automation.cpython-37.pyc,,
comtypes/__pycache__/clear_cache.cpython-37.pyc,,
comtypes/__pycache__/connectionpoints.cpython-37.pyc,,
comtypes/__pycache__/errorinfo.cpython-37.pyc,,
comtypes/__pycache__/git.cpython-37.pyc,,
comtypes/__pycache__/hresult.cpython-37.pyc,,
comtypes/__pycache__/logutil.cpython-37.pyc,,
comtypes/__pycache__/messageloop.cpython-37.pyc,,
comtypes/__pycache__/patcher.cpython-37.pyc,,
comtypes/__pycache__/persist.cpython-37.pyc,,
comtypes/__pycache__/safearray.cpython-37.pyc,,
comtypes/__pycache__/shelllink.cpython-37.pyc,,
comtypes/__pycache__/stream.cpython-37.pyc,,
comtypes/__pycache__/typeinfo.cpython-37.pyc,,
comtypes/__pycache__/util.cpython-37.pyc,,
comtypes/__pycache__/viewobject.cpython-37.pyc,,
comtypes/_comobject.py,sha256=3uZ8QVoFWX5lRw96q-fURLszFwBPV8DJOg6FYUO2ONY,30652
comtypes/_idl_stuff.py,sha256=ueULEoyGnrIiXdUdcsdsIx2NvRS--1Tse_WZdsGuv7I,2326
comtypes/_memberspec.py,sha256=_E4His520giijWbm3-0U8ZNbp9tg05-mQc3FeOoVcZM,21787
comtypes/_meta.py,sha256=-y4hTeGmN9SW0adkyQD_73FIzL_tVMhOJOnIuZ_l7Ck,2189
comtypes/_npsupport.py,sha256=2msDIYvuS2UGFxi_c_oYQ4t13f_BnP9wbx2ZQTb1V9A,5244
comtypes/_post_coinit/__init__.py,sha256=DRFYLnjOIsiwu6XHvXafNnKIbz2D-Zgw3XtgeWilflk,725
comtypes/_post_coinit/__pycache__/__init__.cpython-37.pyc,,
comtypes/_post_coinit/__pycache__/bstr.cpython-37.pyc,,
comtypes/_post_coinit/__pycache__/misc.cpython-37.pyc,,
comtypes/_post_coinit/__pycache__/unknwn.cpython-37.pyc,,
comtypes/_post_coinit/bstr.py,sha256=XpsjeftZGJRsTkK_zYEd9EeiGQRNf5_rhG3yRAyRJec,964
comtypes/_post_coinit/misc.py,sha256=QP7gj7gx91iY3EMLvXp7l6j89Rbbi7jjBloa9-rZLsI,10991
comtypes/_post_coinit/unknwn.py,sha256=OUFSK0bB8Ta9hftR3DsuL1acuW2RwU9DHnHCSUakRas,21783
comtypes/_py_instance_method.py,sha256=o2v4JGYOfgs1YMTarxSk7Ef9EuMSUNbN8NjssO1IzTE,371
comtypes/_safearray.py,sha256=zWPp7Mz_X-dTuVtp7VAyY9T_0grfFEPxufqfYaYBDYs,4569
comtypes/_tlib_version_checker.py,sha256=gxQNW5BhKch3Gds9mBSAnsFkceU4vWzMtYDhfDt28OA,616
comtypes/automation.py,sha256=Nef_Og3ZZZAGikXObK0XSLAXzA3bxAPmXESCq3Tvsz8,35191
comtypes/clear_cache.py,sha256=J2MHIgMf9b3DLxR6uINg611cLM81IyVEzsk1R-5iZPs,1759
comtypes/client/__init__.py,sha256=7oPeZPtS9U_YB_9zw1N3-PjZfMPWez4o09gmlnSMI_8,11936
comtypes/client/__pycache__/__init__.cpython-37.pyc,,
comtypes/client/__pycache__/_code_cache.cpython-37.pyc,,
comtypes/client/__pycache__/_constants.cpython-37.pyc,,
comtypes/client/__pycache__/_events.cpython-37.pyc,,
comtypes/client/__pycache__/_generate.cpython-37.pyc,,
comtypes/client/__pycache__/dynamic.cpython-37.pyc,,
comtypes/client/__pycache__/lazybind.cpython-37.pyc,,
comtypes/client/_code_cache.py,sha256=U2LlHtxZJCzBRg6i3D_7-FW1v75lb1fFtHsuI06DgRw,5809
comtypes/client/_constants.py,sha256=x1rdfZl2BCpnxDo-nTtIJdQhhZSij_k35qOchNAwlUs,4579
comtypes/client/_events.py,sha256=vbxY3sLlTvEYxsszVMsTuBrGNDn852UJHeS_o9_78OU,11198
comtypes/client/_generate.py,sha256=ESA3tiEt4i2GCHAtTb_dz9rkYv9MnscvfHPRKelB1ko,12048
comtypes/client/dynamic.py,sha256=8KMzIApIVfWyshXqVTs4cwriib0Uj5zWsF4uCk0FJjE,6032
comtypes/client/lazybind.py,sha256=Fd9IQAxxDqxmK8m_FB2F5hY0s1AEbJ8dsLI2FRlAdUQ,9230
comtypes/connectionpoints.py,sha256=RzO2ObveyMp9SrB04ZvZ1DOi4sl39F1sXtWZi7Wa2LI,3388
comtypes/errorinfo.py,sha256=7wkYnFmeyCGNHqHaKUcZCinCkKt6ju89GIufuvxTpvs,3855
comtypes/git.py,sha256=S0kXaDdO_29kqWIf2FhDBsbpBvFBhnmJliyNb8b8jvI,2730
comtypes/hints.pyi,sha256=gLmk5Y-XlWQAEeK_ys_f9b1SZZ2XIkUqbgg4yCYZ_dQ,10740
comtypes/hresult.py,sha256=Bv8wb7twndLE2jWG7U0Qis4hEH4waiEh91Qsi_K7IYU,2431
comtypes/logutil.py,sha256=c8HT1FEXhZAeFhBta6iKy3ev_Ygk6loi58EYsvx2Jm8,1697
comtypes/messageloop.py,sha256=2W1YnMlvVFHy_TH6Z8MwxP7jjy3808kInY4K-wvloBU,1311
comtypes/patcher.py,sha256=S4OHRtMn3y53okBWf_8o3BmLshOVlVT0Pu1PCgOgqIs,2046
comtypes/persist.py,sha256=Xx1emyhoFCdCJNrsYAjKzQ3c6Brf9oF4qshHFRuKCsE,8399
comtypes/safearray.py,sha256=Y20LzqpIQoXJ2_JiKKhjtYfLVu4fKt-TXnrpQJxmhrg,17532
comtypes/server/__init__.py,sha256=D1OVF8TmIR7C0C7F0DbHOkp6ZQUK0WaSFqpBmRxNH5M,2378
comtypes/server/__pycache__/__init__.cpython-37.pyc,,
comtypes/server/__pycache__/automation.cpython-37.pyc,,
comtypes/server/__pycache__/connectionpoints.cpython-37.pyc,,
comtypes/server/__pycache__/inprocserver.cpython-37.pyc,,
comtypes/server/__pycache__/localserver.cpython-37.pyc,,
comtypes/server/__pycache__/register.cpython-37.pyc,,
comtypes/server/__pycache__/w_getopt.cpython-37.pyc,,
comtypes/server/automation.py,sha256=UktWMW6WhvvyoFiHJ9lhp-4rO_j_3uhc_WlDG-6lalk,2887
comtypes/server/connectionpoints.py,sha256=qHOiHeHSo5blHPmVZXnADeeL4ait6fTrtw2gAT4q4no,6806
comtypes/server/inprocserver.py,sha256=bBX103MBbpYT7eWs4snrEdXn_vHBZzU35rHHwyRpZZY,4390
comtypes/server/localserver.py,sha256=3KVG8IplqGZq4z8gqoKH4rz1LirQ7Sf5rVsthW4DGZE,2292
comtypes/server/register.py,sha256=_lc8T28IBpPmwK6nQxaZb_9QJSF8OdWOtXsIYtdyMls,15120
comtypes/server/w_getopt.py,sha256=2gWUcGPvJ1v5phB2KclBbRS5X3SaRsKeY_sC8xs1Qr4,2648
comtypes/shelllink.py,sha256=c-hPmkAlieRJFl3uPIBgAU25vHn-ZYxxd8ttxgh4e_c,8845
comtypes/stream.py,sha256=qM9U9JKKOxG9hPYAhbDlzrJ9lLLKheTbrhGugx-VugY,2502
comtypes/test/TestComServer.idl,sha256=72f38383VDU7opBvh6Mc_iE95QC5rQ-vR9kPX81n-9M,2487
comtypes/test/TestComServer.py,sha256=gDLLItkfr0YG1nHgsKmh0iuPAxd65mpH3ys8Hwt-8-4,5150
comtypes/test/TestComServer.tlb,sha256=fdwAtGi-2skbrTHBEoOImEg48rxglmeY0H5IuD5_3Sk,3560
comtypes/test/TestDispServer.idl,sha256=SKdlb77Kj7pxkTX1BRYV04htcq0PDBq59ei2lMFuSI4,1835
comtypes/test/TestDispServer.py,sha256=SbxR7KWF-M3X7PMDO2u2OEexyOW7v2iCjsyHvKXPNrM,3735
comtypes/test/TestDispServer.tlb,sha256=eKi5NpCurHwgql_zGzc1SawWjSMbDojMRyrVj_CrRao,2992
comtypes/test/__init__.py,sha256=6iokn45Bm4mc4LI5QqhGbzGAuYWkV3Qqx56Z8UeeFNg,8262
comtypes/test/__pycache__/TestComServer.cpython-37.pyc,,
comtypes/test/__pycache__/TestDispServer.cpython-37.pyc,,
comtypes/test/__pycache__/__init__.cpython-37.pyc,,
comtypes/test/__pycache__/find_memleak.cpython-37.pyc,,
comtypes/test/__pycache__/runtests.cpython-37.pyc,,
comtypes/test/__pycache__/setup.cpython-37.pyc,,
comtypes/test/__pycache__/test_BSTR.cpython-37.pyc,,
comtypes/test/__pycache__/test_DISPPARAMS.cpython-37.pyc,,
comtypes/test/__pycache__/test_GUID.cpython-37.pyc,,
comtypes/test/__pycache__/test_QueryService.cpython-37.pyc,,
comtypes/test/__pycache__/test_agilent.cpython-37.pyc,,
comtypes/test/__pycache__/test_avmc.cpython-37.pyc,,
comtypes/test/__pycache__/test_basic.cpython-37.pyc,,
comtypes/test/__pycache__/test_casesensitivity.cpython-37.pyc,,
comtypes/test/__pycache__/test_clear_cache.cpython-37.pyc,,
comtypes/test/__pycache__/test_client.cpython-37.pyc,,
comtypes/test/__pycache__/test_client_dynamic.cpython-37.pyc,,
comtypes/test/__pycache__/test_client_regenerate_modules.cpython-37.pyc,,
comtypes/test/__pycache__/test_collections.cpython-37.pyc,,
comtypes/test/__pycache__/test_comserver.cpython-37.pyc,,
comtypes/test/__pycache__/test_createwrappers.cpython-37.pyc,,
comtypes/test/__pycache__/test_dict.cpython-37.pyc,,
comtypes/test/__pycache__/test_dispifc_records.cpython-37.pyc,,
comtypes/test/__pycache__/test_dispifc_safearrays.cpython-37.pyc,,
comtypes/test/__pycache__/test_dispinterface.cpython-37.pyc,,
comtypes/test/__pycache__/test_dyndispatch.cpython-37.pyc,,
comtypes/test/__pycache__/test_excel.cpython-37.pyc,,
comtypes/test/__pycache__/test_findgendir.cpython-37.pyc,,
comtypes/test/__pycache__/test_getactiveobj.cpython-37.pyc,,
comtypes/test/__pycache__/test_ie.cpython-37.pyc,,
comtypes/test/__pycache__/test_ienum.cpython-37.pyc,,
comtypes/test/__pycache__/test_imfattributes.cpython-37.pyc,,
comtypes/test/__pycache__/test_inout_args.cpython-37.pyc,,
comtypes/test/__pycache__/test_istream.cpython-37.pyc,,
comtypes/test/__pycache__/test_midl_safearray_create.cpython-37.pyc,,
comtypes/test/__pycache__/test_msscript.cpython-37.pyc,,
comtypes/test/__pycache__/test_npsupport.cpython-37.pyc,,
comtypes/test/__pycache__/test_outparam.cpython-37.pyc,,
comtypes/test/__pycache__/test_propputref.cpython-37.pyc,,
comtypes/test/__pycache__/test_pump_events.cpython-37.pyc,,
comtypes/test/__pycache__/test_recordinfo.cpython-37.pyc,,
comtypes/test/__pycache__/test_safearray.cpython-37.pyc,,
comtypes/test/__pycache__/test_sapi.cpython-37.pyc,,
comtypes/test/__pycache__/test_server.cpython-37.pyc,,
comtypes/test/__pycache__/test_showevents.cpython-37.pyc,,
comtypes/test/__pycache__/test_subinterface.cpython-37.pyc,,
comtypes/test/__pycache__/test_typeannotator.cpython-37.pyc,,
comtypes/test/__pycache__/test_typeinfo.cpython-37.pyc,,
comtypes/test/__pycache__/test_urlhistory.cpython-37.pyc,,
comtypes/test/__pycache__/test_variant.cpython-37.pyc,,
comtypes/test/__pycache__/test_win32com_interop.cpython-37.pyc,,
comtypes/test/__pycache__/test_wmi.cpython-37.pyc,,
comtypes/test/__pycache__/test_word.cpython-37.pyc,,
comtypes/test/find_memleak.py,sha256=UGaoqZabVqzepbhl7Pb2OFeQbyExzmRnsN0kVR3Mt5k,2342
comtypes/test/mylib.idl,sha256=FVfMqxRSaLZCA3K3EFeNPibPv9KApSIPZnOowI2fVEM,1664
comtypes/test/mylib.tlb,sha256=Kvv8JW32DW7iCeBWiQo91D-21OYb7OMNuy5hYY2hPvg,3080
comtypes/test/mytypelib.idl,sha256=ghjqgtzlLYvsptCXq44AS7P85DPJwbLmqTBYJKBU62s,2590
comtypes/test/runtests.py,sha256=f88xsPQ0XMLvsvbQfjMocfrkFRaXFvPZ5RDTFx8sZNs,142
comtypes/test/setup.py,sha256=zR6XK34Z0oTAdF9NiwagBrqVk8MN5nkIL0lVwPltO-Q,170
comtypes/test/test_BSTR.py,sha256=ec_6zIygGj79atd68Wvx-7T_vRXg-Z0akDbx-u798ls,1565
comtypes/test/test_DISPPARAMS.py,sha256=P1VcwZ9k_p3JbK6oFn2Ld0AI413xiHDl3RD9Qjx9fe4,1156
comtypes/test/test_GUID.py,sha256=ViOMAd7J-2fW86kCTBDZqMRq5da5Rm2dl5GCWKkeoRc,1734
comtypes/test/test_QueryService.py,sha256=iCLM0A8911s10WiW9dlOYGjDFWWJZtzAmL83cDp_wHE,834
comtypes/test/test_agilent.py,sha256=oqoLsxxhRzwJJ8xsEERsCYzsxADI3uAwAN_E9fcGC50,4436
comtypes/test/test_avmc.py,sha256=N5gcs4_jyuImcpfDT9E4HXzVRvME_yd2JVKhIprSBDc,1370
comtypes/test/test_basic.py,sha256=Pxi9SqOOKAZ5PGWZ4FZuS4C9LwHDGGPTf7pBsGXZne0,4213
comtypes/test/test_casesensitivity.py,sha256=1hLYAtWMT3SarC8XFBKL1H25phmYYNOQjsA-SUpKKiM,1404
comtypes/test/test_clear_cache.py,sha256=blS3YdJGYOIaeXq5df8bJSZ5np4wsTC75HhS_8dJt0s,816
comtypes/test/test_client.py,sha256=R18GlEkemUC1r3ZAZPPuASK5P-68k2Bv1UVJBwyL_0Y,12934
comtypes/test/test_client_dynamic.py,sha256=SIn7EbdwXldhfcxIJb7-Umcf5wJhUVHHLSRDSjar-Ec,2905
comtypes/test/test_client_regenerate_modules.py,sha256=u-TIA6I-BXyI9CcfQNgl6_6BBLm95JqMMqF0xo4pFB4,7515
comtypes/test/test_collections.py,sha256=oRDh4yvkR8XrLHkq9geanlre_7S-ta3vYYdgzUSBAM8,5441
comtypes/test/test_comserver.py,sha256=t6n8mBy3ELHrgZOgDJ7jaQ9cbE2pM9jOJgk66EkgQdE,7708
comtypes/test/test_createwrappers.py,sha256=fD8pJy0UzhlZdlJ6MfFKYcKsoqBDP8iHvQktjO0-WEA,4167
comtypes/test/test_dict.py,sha256=W3okJD08fa3iygcrUOUNTOOyEPOcVKJPTEp6XP9_FSQ,3280
comtypes/test/test_dispifc_records.py,sha256=QOOMW-XwmQxBiAjZusvTwMB5F4zZw82RIqhnDDBdhAg,4277
comtypes/test/test_dispifc_safearrays.py,sha256=hki1XrFuF5FiASDQXiQLkAaoVINk49KU2IhFgY8QQ_4,3973
comtypes/test/test_dispinterface.py,sha256=7O4dU3uSWZqLHuvQgRV-w2MbswC19ODLqXd8QaCeCFc,4824
comtypes/test/test_dyndispatch.py,sha256=myU_oq4nNoq7wLMtZGKfko0ZIuiWeU0FdztZoL6y37c,3105
comtypes/test/test_excel.py,sha256=WBjqAe1P2BdQP0a_fdGQxXJkTnwww91J_0dlCYSHDQo,4768
comtypes/test/test_findgendir.py,sha256=rQJ6m3C2o_S2tUFnWt-A5O8Qkhyr4jYyUEixUtIHjbg,3034
comtypes/test/test_getactiveobj.py,sha256=hNwUdVYed6z04wEyygSMWd1qPoPBdCLRQLjlk7CUKv4,1977
comtypes/test/test_ie.py,sha256=PapmpHkuLjh4TYCVhKX1i7s2GNFolNNKsSzNCQWyaKU,3384
comtypes/test/test_ienum.py,sha256=jR395ylpN4BYn5_F_s3NUPnmtkdY8xowhkyO_5DVSTA,887
comtypes/test/test_imfattributes.py,sha256=prCS9lvPuhRdubWaVekuBgiyPCJKb7fNvYpqbjwafxM,1101
comtypes/test/test_inout_args.py,sha256=rMuyi3Ztedoyyf8lVbQeVv21_xzoqihlKqf-LEniHOE,20881
comtypes/test/test_istream.py,sha256=Vdd0Q3YWDMm48-iVTfjHtSzXOaRLo6PAGXqwUstSgj0,1632
comtypes/test/test_jscript.js,sha256=53vJJO4fDUBxIg3-D8Wuldzeejp3_MvImpN6yedPMTI,404
comtypes/test/test_midl_safearray_create.py,sha256=rBLP9GIRFEZzeJsMQ7TFbr96K8e4hDcwTBhGjBhrkZc,3869
comtypes/test/test_msscript.py,sha256=v7g97aHjbzOntatrSv2TUATyx7czrA-Dx1GYt1kD9xw,2877
comtypes/test/test_npsupport.py,sha256=s6vWq9nNzppBrFKy5co-vN7MNOeRFYCz7wTizgl2vG0,12209
comtypes/test/test_outparam.py,sha256=Lw4qIfQR2mmBCWHNBM_zEBS28BhvXEUPy-Zp3cAgPNU,2573
comtypes/test/test_propputref.py,sha256=peLOR5rWoBVIYIuykdb224p98sPk6PunCa6Zmq3uBUo,1373
comtypes/test/test_pump_events.py,sha256=wcOhzSgNkTBRZV_oywfO_sQeI9N-u3Kf9oQHhBmR1Dg,379
comtypes/test/test_recordinfo.py,sha256=6fSXapglv3xaed8cOhHmHBnmkz_7RB9sxSmuGeXF_P8,2723
comtypes/test/test_safearray.py,sha256=HqSXdW6_d6mbgMId1V1IpQKNUmfipgTQ_KPI2zoDjek,10175
comtypes/test/test_sapi.py,sha256=_ZCW_xrS3uBWpLB7pEWOneH9ljnOEC_-QWWiY9kW03w,1146
comtypes/test/test_server.py,sha256=2JFuIuuO9aGGq5t7f5ClyFvul7SfBfzOEbFB6QJz4zo,12147
comtypes/test/test_showevents.py,sha256=GfzALH4K2C9j0cRtdZMjvXTzi8ABMrPmLF_1MGKDkXw,1512
comtypes/test/test_subinterface.py,sha256=ABo9MDNqqo6PFzXKfGlj_Cb-yazUi7o_1LrZYj7MSPM,374
comtypes/test/test_typeannotator.py,sha256=Euw8jdg1ccBkLbp_G1L6nWXOUtu_N0NuWUwMgkPOfV8,6712
comtypes/test/test_typeinfo.py,sha256=XBMv2mC1LSv7TYvhqU6C6LRKCm3PEyoEytyXN7ZjZx4,3721
comtypes/test/test_urlhistory.py,sha256=sMUAhj1p1lCVmbYn9TEdlu62B7qIiGJyFaz99_5EFyI,1825
comtypes/test/test_variant.py,sha256=cfUN5TX9jzUU8pN9eeMb8IE2nC6O1leB6N2ni0r55wE,10871
comtypes/test/test_win32com_interop.py,sha256=EWF2JHmzPGpxgHaAFQeBSnjT13TlNuc4whHoNcbDHMY,4064
comtypes/test/test_wmi.py,sha256=6kMIXvMSiw5pH4S820i2TV-bvLE92WZYAetEE6y2YH0,2370
comtypes/test/test_word.py,sha256=BMK9vrdxy6gOAhknLRkLPFb96tLx3neXGkxVyWhnLNg,2372
comtypes/test/urlhist.tlb,sha256=u2RrUrgUD3R-sPRP8iBa6I3FcM4AyrbgCro64vSGZoU,6480
comtypes/tools/__init__.py,sha256=WgkiD-7kBZcJUSMiqdSjgeRiUgPK07vyu51IJ0mbfcA,30
comtypes/tools/__pycache__/__init__.cpython-37.pyc,,
comtypes/tools/__pycache__/tlbparser.cpython-37.pyc,,
comtypes/tools/__pycache__/typedesc.cpython-37.pyc,,
comtypes/tools/__pycache__/typedesc_base.cpython-37.pyc,,
comtypes/tools/codegenerator/__init__.py,sha256=gfLcwlpIxQhRAvzMdGLuO3Oljul65m4_YVEA3HRYEhg,207
comtypes/tools/codegenerator/__pycache__/__init__.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/codegenerator.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/helpers.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/modulenamer.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/namespaces.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/packing.cpython-37.pyc,,
comtypes/tools/codegenerator/__pycache__/typeannotator.cpython-37.pyc,,
comtypes/tools/codegenerator/codegenerator.py,sha256=Oh42LjZsospc_A9LnnrUYl1eTfJ0cYLrAjr0xICd1cM,41477
comtypes/tools/codegenerator/helpers.py,sha256=TVXi_1DntPZC34-1E7LBAeRoiMfJcMD9PxTQoFywvgU,13007
comtypes/tools/codegenerator/modulenamer.py,sha256=PMvrGAL6U-BbkfvnbI1X_Vv4_Tj7S5TJRLKfDcKTCYs,762
comtypes/tools/codegenerator/namespaces.py,sha256=0P7XcQkerNXz2tL_PGrUQfMIqOldkOART9ZxqZOczLU,7198
comtypes/tools/codegenerator/packing.py,sha256=88QgsJ2rPqNXTWbNsvDzxgehrK_LAz-TYM0NS5kJpbU,2480
comtypes/tools/codegenerator/typeannotator.py,sha256=jwExtXSTQlnyXECtvIW_jlzMw8phUvm0G_XDIslEugg,14435
comtypes/tools/tlbparser.py,sha256=i2bjbTtfIVMhJ4cGUKqXXP1pRHf2sQfMhDaYmdkxuVk,30852
comtypes/tools/typedesc.py,sha256=2atF0zgi7qy2p5lM3H6HxPdoK4uZ5xYJK7tpuxCj5m0,6971
comtypes/tools/typedesc_base.py,sha256=X8wB4ST16ocCABg1iEZIkoS4mK3mqGUxRxS6Nu6IN1E,6609
comtypes/typeinfo.py,sha256=GulzaeceCtGz9oiwJgcPDQIkAuS7Ah_978GBDExAc7c,42279
comtypes/util.py,sha256=TbUUnbvBgmRKe0wV_XwTW1wOlXL5-QrP1MIHCwYkOSo,3005
comtypes/viewobject.py,sha256=VOllSjL9hc2sEKBeWrJj8KYgoncRFFVOs-bstZRXozk,6601
