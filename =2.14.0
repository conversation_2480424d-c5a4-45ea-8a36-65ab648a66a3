Collecting anthropic
  Using cached anthropic-0.38.0-py3-none-any.whl.metadata (21 kB)
Requirement already satisfied: matplotlib in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (3.5.3)
Requirement already satisfied: numpy in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (1.21.6)
Requirement already satisfied: opencv-python in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (4.9.0.80)
Requirement already satisfied: pillow in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (9.5.0)
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (2.31.0)
Collecting scikit-learn
  Using cached scikit_learn-1.0.2-cp37-cp37m-win_amd64.whl.metadata (10 kB)
Requirement already satisfied: streamlit in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (1.23.1)
Requirement already satisfied: tensorflow in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (2.11.0)
Requirement already satisfied: pyttsx3 in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (2.99)
Requirement already satisfied: scikit-image in c:\users\<USER>\appdata\local\programs\python\python37\lib\site-packages (0.19.3)
Collecting anyio<5,>=3.5.0 (from anthropic)
  Using cached anyio-3.7.1-py3-none-any.whl.metadata (4.7 kB)
Collecting cached-property (from anthropic)
  Using cached cached_property-1.5.2-py2.py3-none-any.whl.metadata (11 kB)
Collecting distro<2,>=1.7.0 (from anthropic)
  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting httpx<1,>=0.23.0 (from anthropic)
  Using cached httpx-0.24.1-py3-none-any.whl.metadata (7.4 kB)
INFO: pip is looking at multiple versions of anthropic to determine which version is compatible with other requirements. This could take a while.
Collecting anthropic
  Using cached anthropic-0.37.1-py3-none-any.whl.metadata (21 kB)
  Using cached anthropic-0.37.0-py3-none-any.whl.metadata (21 kB)
  Using cached anthropic-0.36.2-py3-none-any.whl.metadata (21 kB)
  Using cached anthropic-0.36.1-py3-none-any.whl.metadata (21 kB)
  Using cached anthropic-0.36.0-py3-none-any.whl.metadata (21 kB)
  Using cached anthropic-0.35.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.34.2-py3-none-any.whl.metadata (18 kB)
INFO: pip is still looking at multiple versions of anthropic to determine which version is compatible with other requirements. This could take a while.
  Using cached anthropic-0.34.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.34.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.33.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.33.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.32.0-py3-none-any.whl.metadata (18 kB)
INFO: This is taking longer than usual. You might need to provide the dependency resolver with stricter constraints to reduce runtime. See https://pip.pypa.io/warnings/backtracking for guidance. If you want to abort this run, press Ctrl + C.
  Using cached anthropic-0.31.2-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.31.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.31.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.30.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.30.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.29.2-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.29.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.28.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.28.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.27.0-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.26.1-py3-none-any.whl.metadata (18 kB)
  Using cached anthropic-0.26.0-py3-none-any.whl.metadata (18 kB)
Collecting pydantic<3,>=1.9.0 (from anthropic)
  Using cached pydantic-2.5.3-py3-none-any.whl.metadata (65 kB)
Collecting sniffio (from anthropic)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting tokenizers>=0.13.0 (from anthropic)
  Using cached tokenizers-0.21.0.tar.gz (343 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Installing backend dependencies: started
  Installing backend dependencies: finished with status 'error'
